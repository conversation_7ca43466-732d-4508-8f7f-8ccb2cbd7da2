using UnityEngine;

/// <summary>
/// 主要的地形生成控制器
/// 负责调用算法、生成地图、处理可视化和实时更新
/// </summary>
public class NoiseMap : MonoBehaviour
{
    [Header("配置")]
    [SerializeField] private NoiseConfigSO noiseConfig;
    [SerializeField] private GameObject quadObject; // 用于显示地图的QUAD对象

    [Header("可视化设置")]
    [SerializeField] private bool showElevation = true;
    [SerializeField] private bool showMoisture = false;
    [SerializeField] private bool showBiomes = false;

    // 生成的地图数据
    private float[,] elevationMap;
    private float[,] moistureMap;
    private Texture2D mapTexture;
    private Renderer quadRenderer;

    // 用于实时更新检测
    private NoiseConfigSO lastConfig;
    private int lastConfigHash;

    void Start()
    {
        // 获取QUAD对象的渲染器组件
        if (quadObject != null)
        {
            quadRenderer = quadObject.GetComponent<Renderer>();
            if (quadRenderer == null)
            {
                Debug.LogError("指定的QUAD对象没有Renderer组件！");
                return;
            }
        }
        else
        {
            Debug.LogError("未指定QUAD对象来显示地图！");
            return;
        }

        // 初始生成地图
        if (noiseConfig != null)
        {
            GenerateMap();
        }
        else
        {
            Debug.LogWarning("未设置NoiseConfigSO，请在Inspector中分配配置文件！");
        }
    }

    void Update()
    {
        // 检查是否需要实时更新
        if (noiseConfig != null && noiseConfig.AutoUpdate && ShouldUpdateMap())
        {
            GenerateMap();
        }
    }

    /// <summary>
    /// 生成完整的地图
    /// </summary>
    public void GenerateMap()
    {
        if (noiseConfig == null)
        {
            Debug.LogError("无法生成地图：NoiseConfigSO为空！");
            return;
        }

        // 记录开始时间用于性能分析
        float startTime = Time.realtimeSinceStartup;

        // 初始化地图数组
        int width = noiseConfig.MapWidth;
        int height = noiseConfig.MapHeight;
        elevationMap = new float[width, height];
        moistureMap = new float[width, height];

        // 生成噪声数据
        GenerateNoiseData();

        // 创建并更新纹理
        UpdateMapTexture();

        // 更新配置哈希用于实时更新检测
        UpdateConfigHash();

        // 输出性能信息
        float generationTime = Time.realtimeSinceStartup - startTime;
        Debug.Log($"地图生成完成！尺寸: {width}x{height}, 耗时: {generationTime:F3}秒");
    }

    /// <summary>
    /// 生成噪声数据
    /// </summary>
    private void GenerateNoiseData()
    {
        int width = noiseConfig.MapWidth;
        int height = noiseConfig.MapHeight;

        // 并行生成噪声数据以提高性能
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                // 生成高度噪声
                elevationMap[x, y] = NoiseAlgorithm.GenerateElevationNoise(x, y, noiseConfig);

                // 生成湿度噪声
                moistureMap[x, y] = NoiseAlgorithm.GenerateMoistureNoise(x, y, noiseConfig);
            }
        }
    }

    /// <summary>
    /// 更新地图纹理
    /// </summary>
    private void UpdateMapTexture()
    {
        int width = noiseConfig.MapWidth;
        int height = noiseConfig.MapHeight;

        // 创建新纹理
        if (mapTexture == null || mapTexture.width != width || mapTexture.height != height)
        {
            if (mapTexture != null)
            {
                DestroyImmediate(mapTexture);
            }
            mapTexture = new Texture2D(width, height, TextureFormat.RGB24, false);
            mapTexture.filterMode = FilterMode.Point; // 使用点过滤以保持像素清晰
        }

        // 填充纹理像素
        Color[] pixels = new Color[width * height];
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                int index = y * width + x;
                pixels[index] = GetPixelColor(x, y);
            }
        }

        // 应用纹理
        mapTexture.SetPixels(pixels);
        mapTexture.Apply();

        // 设置到QUAD材质
        if (quadRenderer != null && quadRenderer.material != null)
        {
            quadRenderer.material.mainTexture = mapTexture;
        }
    }

    /// <summary>
    /// 获取指定位置的像素颜色
    /// </summary>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <returns>像素颜色</returns>
    private Color GetPixelColor(int x, int y)
    {
        float elevation = elevationMap[x, y];
        float moisture = moistureMap[x, y];

        if (showBiomes)
        {
            return GetBiomeColor(elevation, moisture);
        }
        else if (showMoisture)
        {
            return new Color(0f, 0f, moisture); // 蓝色表示湿度
        }
        else // showElevation
        {
            return new Color(elevation, elevation, elevation); // 灰度表示高度
        }
    }

    /// <summary>
    /// 根据高度和湿度获取生物群系颜色
    /// </summary>
    /// <param name="elevation">高度值 (0-1)</param>
    /// <param name="moisture">湿度值 (0-1)</param>
    /// <returns>生物群系颜色</returns>
    private Color GetBiomeColor(float elevation, float moisture)
    {
        // 简化的生物群系分类
        if (elevation < 0.1f) return Color.blue; // 水域
        if (elevation < 0.2f) return Color.yellow; // 海滩
        if (elevation > 0.8f) return Color.white; // 雪山

        // 根据湿度决定颜色
        if (moisture < 0.3f) return new Color(0.8f, 0.7f, 0.4f); // 沙漠
        if (moisture < 0.6f) return Color.green; // 草原
        return new Color(0.2f, 0.6f, 0.2f); // 森林
    }

    /// <summary>
    /// 检查是否需要更新地图
    /// </summary>
    /// <returns>如果需要更新返回true</returns>
    private bool ShouldUpdateMap()
    {
        if (noiseConfig != lastConfig)
        {
            lastConfig = noiseConfig;
            return true;
        }

        int currentHash = GetConfigHash();
        if (currentHash != lastConfigHash)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 更新配置哈希值
    /// </summary>
    private void UpdateConfigHash()
    {
        lastConfigHash = GetConfigHash();
        lastConfig = noiseConfig;
    }

    /// <summary>
    /// 计算配置的哈希值用于变化检测
    /// </summary>
    /// <returns>配置哈希值</returns>
    private int GetConfigHash()
    {
        if (noiseConfig == null) return 0;

        // 简单的哈希计算，包含主要参数
        int hash = noiseConfig.Seed;
        hash = hash * 31 + noiseConfig.MapWidth;
        hash = hash * 31 + noiseConfig.MapHeight;
        hash = hash * 31 + noiseConfig.ElevationExponent.GetHashCode();
        hash = hash * 31 + noiseConfig.ElevationScale.GetHashCode();
        hash = hash * 31 + noiseConfig.MoistureScale.GetHashCode();
        hash = hash * 31 + noiseConfig.EnableIslandMode.GetHashCode();
        hash = hash * 31 + noiseConfig.IslandMixFactor.GetHashCode();
        hash = hash * 31 + noiseConfig.DistanceFunctionType.GetHashCode();

        // 包含振幅数组
        if (noiseConfig.ElevationAmplitudes != null)
        {
            foreach (float amplitude in noiseConfig.ElevationAmplitudes)
            {
                hash = hash * 31 + amplitude.GetHashCode();
            }
        }

        if (noiseConfig.MoistureAmplitudes != null)
        {
            foreach (float amplitude in noiseConfig.MoistureAmplitudes)
            {
                hash = hash * 31 + amplitude.GetHashCode();
            }
        }

        return hash;
    }

    /// <summary>
    /// 获取指定位置的高度值
    /// </summary>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <returns>高度值 (0-1)</returns>
    public float GetElevationAt(int x, int y)
    {
        if (elevationMap == null || x < 0 || x >= elevationMap.GetLength(0) || y < 0 || y >= elevationMap.GetLength(1))
        {
            return 0f;
        }
        return elevationMap[x, y];
    }

    /// <summary>
    /// 获取指定位置的湿度值
    /// </summary>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <returns>湿度值 (0-1)</returns>
    public float GetMoistureAt(int x, int y)
    {
        if (moistureMap == null || x < 0 || x >= moistureMap.GetLength(0) || y < 0 || y >= moistureMap.GetLength(1))
        {
            return 0f;
        }
        return moistureMap[x, y];
    }

    /// <summary>
    /// 手动触发地图重新生成
    /// </summary>
    [ContextMenu("重新生成地图")]
    public void RegenerateMap()
    {
        GenerateMap();
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    void OnDestroy()
    {
        if (mapTexture != null)
        {
            DestroyImmediate(mapTexture);
        }
    }
}