# 噪声地图生成系统

基于Red Blob Games技术方案实现的Unity噪声地图生成系统。

## 功能特性

- ✅ **多层噪声生成**: 支持多个频率层的Perlin噪声合成
- ✅ **种子固定**: 使用种子确保地图的可重现性
- ✅ **图形偏移完整性**: 高度和湿度噪声使用不同偏移确保独立性
- ✅ **边缘平滑过渡**: 支持岛屿模式，提供方形和圆形两种距离函数
- ✅ **实时参数调整**: 修改参数后自动重新生成地图
- ✅ **生物群系支持**: 基于高度和湿度的生物群系分类
- ✅ **性能优化**: 高效的噪声计算和纹理更新

## 文件结构

```
Assets/Scripts/Map/
├── NoiseConfigSO.cs      # ScriptableObject配置文件
├── NoiseAlgorithm.cs     # 独立的噪声算法实现
├── NoiseMap.cs           # 主要的地形生成控制器
├── Editor/
│   └── NoiseMapEditor.cs # 自定义编辑器工具
└── README.md             # 使用说明文档
```

## 快速开始

### 1. 创建噪声配置

1. 在Project窗口右键 → Create → Map → Noise Config
2. 命名为 "DefaultNoiseConfig"
3. 在Inspector中调整参数：
   - **种子**: 控制随机性
   - **地图尺寸**: 设置宽度和高度
   - **高度噪声**: 调整振幅数组和指数
   - **湿度噪声**: 调整振幅数组
   - **边缘平滑**: 启用岛屿模式并调整参数

### 2. 设置地图生成器

1. 创建一个空的GameObject
2. 添加NoiseMap组件
3. 添加MeshRenderer组件（用于显示地图）
4. 创建一个材质并分配给MeshRenderer
5. 将NoiseConfigSO拖拽到NoiseMap的配置槽

### 3. 生成地图

- 运行游戏，地图将自动生成
- 在编辑器中修改NoiseConfigSO参数，地图会实时更新
- 使用自定义编辑器的测试工具进行快速测试

## 参数说明

### 基础设置
- **Seed**: 随机种子，相同种子产生相同地图
- **Map Width/Height**: 地图的像素尺寸

### 高度噪声设置
- **Elevation Amplitudes**: 各频率层的振幅数组，如[1.0, 0.5, 0.25]
- **Elevation Exponent**: 高度重分布指数，>1创建平坦山谷，<1创建尖锐山峰
- **Elevation Scale**: 噪声缩放因子，影响地形特征大小

### 湿度噪声设置
- **Moisture Amplitudes**: 湿度噪声的振幅数组
- **Moisture Scale**: 湿度噪声的缩放因子

### 边缘平滑设置
- **Enable Island Mode**: 启用岛屿模式
- **Island Mix Factor**: 边缘平滑强度 (0-1)
- **Distance Function**: 距离函数类型
  - Square Bump: 方形凸起，适合方形地图
  - Euclidean: 欧几里得距离，适合圆形岛屿

### 高度颜色配置
基于Red Blob Games的颜色方案，提供丰富的地形颜色：

#### 颜色分类
- **水域和海滩**: 深水、浅水、海岸、沙滩
- **低地**: 草地、森林
- **丘陵**: 丘陵、岩石
- **高山**: 山地、雪地

#### 高度阈值
每种地形类型都有对应的高度阈值，可以在Inspector中调整：
- Water Level (0.10): 深水区域
- Shore Level (0.15): 浅水区域
- Sand Level (0.20): 沙滩区域
- Grass Level (0.35): 草地区域
- Forest Level (0.50): 森林区域
- Hills Level (0.65): 丘陵区域
- Rocks Level (0.75): 岩石区域
- Mountain Level (0.85): 山地区域
- 高于Mountain Level: 雪地区域

#### 颜色模式
- **标准模式**: 使用阶梯式颜色分级
- **平滑模式**: 启用"Use Smooth Colors"获得渐变效果

## 可视化模式

NoiseMap组件支持四种可视化模式：
- **Show Elevation**: 显示高度图，使用配置的地形颜色
  - 可选择标准模式或平滑插值模式
  - 颜色完全基于Red Blob Games的配色方案
- **Show Moisture**: 显示湿度图（蓝色渐变）
- **Show Biomes**: 显示生物群系（简化的彩色分类）
- **Use Smooth Colors**: 在高度模式下启用颜色平滑插值

## 生物群系分类

基于高度和湿度值的生物群系：
- 海洋、海滩（低海拔）
- 沙漠、草原、森林（中海拔）
- 苔原、雪地（高海拔）

## 性能考虑

- 地图尺寸直接影响生成时间：256x256约需10-50ms
- 振幅数组长度影响噪声质量和计算量
- 实时更新仅在参数改变时触发
- 使用点过滤模式保持像素清晰度

## 扩展建议

1. **添加更多噪声类型**: 实现Simplex、OpenSimplex等
2. **河流系统**: 基于高度梯度生成河流
3. **温度系统**: 结合纬度和海拔计算温度
4. **季节变化**: 动态调整生物群系
5. **无限地图**: 实现基于摄像机位置的动态生成

## 故障排除

### 常见问题

**Q: 地图不显示**
A: 检查是否设置了NoiseConfigSO和MeshRenderer组件

**Q: 地图不更新**
A: 确保AutoUpdate已启用，或手动调用RegenerateMap()

**Q: 地图看起来很模糊**
A: 检查纹理过滤模式，建议使用Point过滤

**Q: 性能问题**
A: 减少地图尺寸或振幅数组长度

### 调试工具

使用NoiseMapEditor提供的工具：
- 随机种子测试
- 岛屿模式切换
- 配置验证
- 性能信息查看

## 技术参考

本实现基于以下技术方案：
- [Red Blob Games - Making maps with noise functions](https://www.redblobgames.com/maps/terrain-from-noise/)
- Unity Perlin噪声函数
- ScriptableObject配置系统
