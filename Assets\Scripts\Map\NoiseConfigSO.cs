using UnityEngine;

[CreateAssetMenu(fileName = "NoiseConfig", menuName = "Map/Noise Config")]
public class NoiseConfigSO : ScriptableObject
{
    [Header("基础设置")]
    [SerializeField] private int seed = 12345;
    [SerializeField] private int mapWidth = 256;
    [SerializeField] private int mapHeight = 256;

    [Header("高度噪声设置")]
    [SerializeField] private float[] elevationAmplitudes = { 1.0f, 0.5f, 0.25f, 0.125f, 0.0625f };
    [SerializeField] private float elevationExponent = 2.0f;
    [SerializeField] private float elevationScale = 1.0f;

    [Header("湿度噪声设置")]
    [SerializeField] private float[] moistureAmplitudes = { 1.0f, 0.5f, 0.33f, 0.25f, 0.2f };
    [SerializeField] private float moistureScale = 1.0f;

    [Header("边缘平滑设置")]
    [SerializeField] private bool enableIslandMode = false;
    [SerializeField] private float islandMixFactor = 0.5f;
    [SerializeField] private DistanceFunction distanceFunction = DistanceFunction.SquareBump;

    [Header("实时更新")]
    [SerializeField] private bool autoUpdate = true;

    // 距离函数类型枚举
    public enum DistanceFunction
    {
        SquareBump,     // 方形凸起 - 适合方形地图
        Euclidean       // 欧几里得距离 - 适合圆形岛屿
    }

    // 公共属性访问器
    public int Seed => seed;
    public int MapWidth => mapWidth;
    public int MapHeight => mapHeight;
    public float[] ElevationAmplitudes => elevationAmplitudes;
    public float ElevationExponent => elevationExponent;
    public float ElevationScale => elevationScale;
    public float[] MoistureAmplitudes => moistureAmplitudes;
    public float MoistureScale => moistureScale;
    public bool EnableIslandMode => enableIslandMode;
    public float IslandMixFactor => islandMixFactor;
    public DistanceFunction DistanceFunctionType => distanceFunction;
    public bool AutoUpdate => autoUpdate;

    // 计算振幅总和的辅助方法
    public float GetElevationAmplitudeSum()
    {
        float sum = 0f;
        foreach (float amplitude in elevationAmplitudes)
        {
            sum += amplitude;
        }
        return sum;
    }

    public float GetMoistureAmplitudeSum()
    {
        float sum = 0f;
        foreach (float amplitude in moistureAmplitudes)
        {
            sum += amplitude;
        }
        return sum;
    }

    // 验证参数有效性
    private void OnValidate()
    {
        // 确保地图尺寸为正数
        mapWidth = Mathf.Max(1, mapWidth);
        mapHeight = Mathf.Max(1, mapHeight);

        // 确保指数为正数
        elevationExponent = Mathf.Max(0.1f, elevationExponent);

        // 确保缩放因子为正数
        elevationScale = Mathf.Max(0.1f, elevationScale);
        moistureScale = Mathf.Max(0.1f, moistureScale);

        // 确保岛屿混合因子在0-1范围内
        islandMixFactor = Mathf.Clamp01(islandMixFactor);

        // 确保振幅数组不为空
        if (elevationAmplitudes == null || elevationAmplitudes.Length == 0)
        {
            elevationAmplitudes = new float[] { 1.0f, 0.5f, 0.25f };
        }

        if (moistureAmplitudes == null || moistureAmplitudes.Length == 0)
        {
            moistureAmplitudes = new float[] { 1.0f, 0.5f, 0.33f };
        }
    }
}