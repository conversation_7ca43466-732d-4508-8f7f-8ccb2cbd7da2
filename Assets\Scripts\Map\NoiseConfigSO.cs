using UnityEngine;

[CreateAssetMenu(fileName = "NoiseConfig", menuName = "Map/Noise Config")]
public class NoiseConfigSO : ScriptableObject
{
    [Header("基础设置")]
    [SerializeField] private int seed = 12345;
    [SerializeField] private int mapWidth = 256;
    [SerializeField] private int mapHeight = 256;

    [Header("高度噪声设置")]
    [SerializeField] private float[] elevationAmplitudes = { 1.0f, 0.5f, 0.25f, 0.125f, 0.0625f };
    [SerializeField] private float elevationExponent = 2.0f;
    [SerializeField] private float elevationScale = 1.0f;

    [Header("湿度噪声设置")]
    [SerializeField] private float[] moistureAmplitudes = { 1.0f, 0.5f, 0.33f, 0.25f, 0.2f };
    [SerializeField] private float moistureScale = 1.0f;

    [Header("边缘平滑设置")]
    [SerializeField] private bool enableIslandMode = false;
    [SerializeField] private float islandMixFactor = 0.5f;
    [SerializeField] private DistanceFunction distanceFunction = DistanceFunction.SquareBump;

    [Header("高度颜色配置")]
    [SerializeField] private ElevationColorSettings elevationColors = new ElevationColorSettings();

    [Header("实时更新")]
    [SerializeField] private bool autoUpdate = true;

    // 距离函数类型枚举
    public enum DistanceFunction
    {
        SquareBump,     // 方形凸起 - 适合方形地图
        Euclidean       // 欧几里得距离 - 适合圆形岛屿
    }

    // 公共属性访问器
    public int Seed => seed;
    public int MapWidth => mapWidth;
    public int MapHeight => mapHeight;
    public float[] ElevationAmplitudes => elevationAmplitudes;
    public float ElevationExponent => elevationExponent;
    public float ElevationScale => elevationScale;
    public float[] MoistureAmplitudes => moistureAmplitudes;
    public float MoistureScale => moistureScale;
    public bool EnableIslandMode => enableIslandMode;
    public float IslandMixFactor => islandMixFactor;
    public DistanceFunction DistanceFunctionType => distanceFunction;
    public ElevationColorSettings ElevationColors => elevationColors;
    public bool AutoUpdate => autoUpdate;

    // 计算振幅总和的辅助方法
    public float GetElevationAmplitudeSum()
    {
        float sum = 0f;
        foreach (float amplitude in elevationAmplitudes)
        {
            sum += amplitude;
        }
        return sum;
    }

    public float GetMoistureAmplitudeSum()
    {
        float sum = 0f;
        foreach (float amplitude in moistureAmplitudes)
        {
            sum += amplitude;
        }
        return sum;
    }

    // 验证参数有效性
    private void OnValidate()
    {
        // 确保地图尺寸为正数
        mapWidth = Mathf.Max(1, mapWidth);
        mapHeight = Mathf.Max(1, mapHeight);

        // 确保指数为正数
        elevationExponent = Mathf.Max(0.1f, elevationExponent);

        // 确保缩放因子为正数
        elevationScale = Mathf.Max(0.1f, elevationScale);
        moistureScale = Mathf.Max(0.1f, moistureScale);

        // 确保岛屿混合因子在0-1范围内
        islandMixFactor = Mathf.Clamp01(islandMixFactor);

        // 确保振幅数组不为空
        if (elevationAmplitudes == null || elevationAmplitudes.Length == 0)
        {
            elevationAmplitudes = new float[] { 1.0f, 0.5f, 0.25f };
        }

        if (moistureAmplitudes == null || moistureAmplitudes.Length == 0)
        {
            moistureAmplitudes = new float[] { 1.0f, 0.5f, 0.33f };
        }

        // 验证高度颜色配置
        if (elevationColors == null)
        {
            elevationColors = new ElevationColorSettings();
        }
    }
}

/// <summary>
/// 高度颜色配置类
/// 基于Red Blob Games的颜色方案
/// </summary>
[System.Serializable]
public class ElevationColorSettings
{
    [Header("水域和海滩")]
    [SerializeField] private Color deepWater = new Color(0.12f, 0.24f, 0.55f);      // 深水 - 深蓝色
    [SerializeField] private Color shallowWater = new Color(0.16f, 0.31f, 0.67f);   // 浅水 - 蓝色
    [SerializeField] private Color shore = new Color(0.24f, 0.43f, 0.71f);          // 海岸 - 浅蓝色
    [SerializeField] private Color sand = new Color(0.76f, 0.70f, 0.50f);           // 沙滩 - 沙色

    [Header("低地")]
    [SerializeField] private Color grass = new Color(0.33f, 0.42f, 0.18f);          // 草地 - 深绿色
    [SerializeField] private Color forest = new Color(0.26f, 0.37f, 0.15f);         // 森林 - 更深绿色

    [Header("丘陵")]
    [SerializeField] private Color hills = new Color(0.40f, 0.36f, 0.25f);          // 丘陵 - 棕绿色
    [SerializeField] private Color rocks = new Color(0.50f, 0.43f, 0.35f);          // 岩石 - 灰棕色

    [Header("高山")]
    [SerializeField] private Color mountain = new Color(0.60f, 0.57f, 0.54f);       // 山地 - 灰色
    [SerializeField] private Color snow = new Color(0.95f, 0.95f, 0.95f);           // 雪地 - 白色

    // 高度阈值设置
    [Header("高度阈值")]
    [Range(0f, 1f)][SerializeField] private float waterLevel = 0.10f;
    [Range(0f, 1f)][SerializeField] private float shoreLevel = 0.15f;
    [Range(0f, 1f)][SerializeField] private float sandLevel = 0.20f;
    [Range(0f, 1f)][SerializeField] private float grassLevel = 0.35f;
    [Range(0f, 1f)][SerializeField] private float forestLevel = 0.50f;
    [Range(0f, 1f)][SerializeField] private float hillsLevel = 0.65f;
    [Range(0f, 1f)][SerializeField] private float rocksLevel = 0.75f;
    [Range(0f, 1f)][SerializeField] private float mountainLevel = 0.85f;

    // 公共属性访问器
    public Color DeepWater => deepWater;
    public Color ShallowWater => shallowWater;
    public Color Shore => shore;
    public Color Sand => sand;
    public Color Grass => grass;
    public Color Forest => forest;
    public Color Hills => hills;
    public Color Rocks => rocks;
    public Color Mountain => mountain;
    public Color Snow => snow;

    public float WaterLevel => waterLevel;
    public float ShoreLevel => shoreLevel;
    public float SandLevel => sandLevel;
    public float GrassLevel => grassLevel;
    public float ForestLevel => forestLevel;
    public float HillsLevel => hillsLevel;
    public float RocksLevel => rocksLevel;
    public float MountainLevel => mountainLevel;

    /// <summary>
    /// 根据高度值获取对应的颜色
    /// </summary>
    /// <param name="elevation">高度值 (0-1)</param>
    /// <returns>对应的颜色</returns>
    public Color GetColorByElevation(float elevation)
    {
        if (elevation < waterLevel) return deepWater;
        if (elevation < shoreLevel) return shallowWater;
        if (elevation < sandLevel) return shore;
        if (elevation < grassLevel) return sand;
        if (elevation < forestLevel) return grass;
        if (elevation < hillsLevel) return forest;
        if (elevation < rocksLevel) return hills;
        if (elevation < mountainLevel) return rocks;
        return snow;
    }

    /// <summary>
    /// 获取平滑插值的颜色（可选功能）
    /// </summary>
    /// <param name="elevation">高度值 (0-1)</param>
    /// <returns>插值后的颜色</returns>
    public Color GetSmoothColorByElevation(float elevation)
    {
        // 简单的线性插值实现
        if (elevation < waterLevel) return Color.Lerp(deepWater, shallowWater, elevation / waterLevel);
        if (elevation < shoreLevel) return Color.Lerp(shallowWater, shore, (elevation - waterLevel) / (shoreLevel - waterLevel));
        if (elevation < sandLevel) return Color.Lerp(shore, sand, (elevation - shoreLevel) / (sandLevel - shoreLevel));
        if (elevation < grassLevel) return Color.Lerp(sand, grass, (elevation - sandLevel) / (grassLevel - sandLevel));
        if (elevation < forestLevel) return Color.Lerp(grass, forest, (elevation - grassLevel) / (forestLevel - grassLevel));
        if (elevation < hillsLevel) return Color.Lerp(forest, hills, (elevation - forestLevel) / (hillsLevel - forestLevel));
        if (elevation < rocksLevel) return Color.Lerp(hills, rocks, (elevation - hillsLevel) / (rocksLevel - hillsLevel));
        if (elevation < mountainLevel) return Color.Lerp(rocks, mountain, (elevation - rocksLevel) / (mountainLevel - rocksLevel));
        return Color.Lerp(mountain, snow, (elevation - mountainLevel) / (1f - mountainLevel));
    }
}