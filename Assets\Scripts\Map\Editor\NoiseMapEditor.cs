using UnityEngine;
using UnityEditor;

/// <summary>
/// NoiseMap的自定义编辑器，提供测试和优化功能
/// </summary>
[CustomEditor(typeof(NoiseMap))]
public class NoiseMapEditor : Editor
{
    private NoiseMap noiseMap;
    private bool showPerformanceInfo = false;
    private bool showTestControls = true;
    
    void OnEnable()
    {
        noiseMap = (NoiseMap)target;
    }
    
    public override void OnInspectorGUI()
    {
        // 绘制默认Inspector
        DrawDefaultInspector();
        
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("测试和优化工具", EditorStyles.boldLabel);
        
        // 测试控制面板
        showTestControls = EditorGUILayout.Foldout(showTestControls, "测试控制");
        if (showTestControls)
        {
            EditorGUILayout.BeginVertical("box");
            
            // 生成地图按钮
            if (GUILayout.Button("重新生成地图", GUILayout.Height(30)))
            {
                noiseMap.RegenerateMap();
            }
            
            EditorGUILayout.BeginHorizontal();
            
            // 测试不同种子
            if (GUILayout.Button("随机种子"))
            {
                var config = GetNoiseConfig();
                if (config != null)
                {
                    Undo.RecordObject(config, "Change Seed");
                    var seedField = typeof(NoiseConfigSO).GetField("seed", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (seedField != null)
                    {
                        seedField.SetValue(config, Random.Range(1, 999999));
                        EditorUtility.SetDirty(config);
                        noiseMap.RegenerateMap();
                    }
                }
            }
            
            // 切换岛屿模式
            if (GUILayout.Button("切换岛屿模式"))
            {
                var config = GetNoiseConfig();
                if (config != null)
                {
                    Undo.RecordObject(config, "Toggle Island Mode");
                    var islandField = typeof(NoiseConfigSO).GetField("enableIslandMode", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (islandField != null)
                    {
                        bool currentValue = (bool)islandField.GetValue(config);
                        islandField.SetValue(config, !currentValue);
                        EditorUtility.SetDirty(config);
                        noiseMap.RegenerateMap();
                    }
                }
            }
            
            EditorGUILayout.EndHorizontal();
            
            EditorGUILayout.EndVertical();
        }
        
        // 性能信息
        showPerformanceInfo = EditorGUILayout.Foldout(showPerformanceInfo, "性能信息");
        if (showPerformanceInfo)
        {
            EditorGUILayout.BeginVertical("box");
            
            var config = GetNoiseConfig();
            if (config != null)
            {
                int totalPixels = config.MapWidth * config.MapHeight;
                int elevationOctaves = config.ElevationAmplitudes?.Length ?? 0;
                int moistureOctaves = config.MoistureAmplitudes?.Length ?? 0;
                int totalNoiseCalculations = totalPixels * (elevationOctaves + moistureOctaves);
                
                EditorGUILayout.LabelField($"地图尺寸: {config.MapWidth} x {config.MapHeight}");
                EditorGUILayout.LabelField($"总像素数: {totalPixels:N0}");
                EditorGUILayout.LabelField($"高度噪声层数: {elevationOctaves}");
                EditorGUILayout.LabelField($"湿度噪声层数: {moistureOctaves}");
                EditorGUILayout.LabelField($"总噪声计算次数: {totalNoiseCalculations:N0}");
                
                // 内存使用估算
                float memoryMB = (totalPixels * 2 * sizeof(float)) / (1024f * 1024f); // 两个float数组
                EditorGUILayout.LabelField($"估算内存使用: {memoryMB:F2} MB");
            }
            
            EditorGUILayout.EndVertical();
        }
        
        // 验证配置
        EditorGUILayout.Space();
        if (GUILayout.Button("验证配置"))
        {
            ValidateConfiguration();
        }
        
        // 如果有修改，标记为脏数据
        if (GUI.changed)
        {
            EditorUtility.SetDirty(target);
        }
    }
    
    /// <summary>
    /// 获取噪声配置
    /// </summary>
    private NoiseConfigSO GetNoiseConfig()
    {
        var configField = typeof(NoiseMap).GetField("noiseConfig", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return configField?.GetValue(noiseMap) as NoiseConfigSO;
    }
    
    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    private void ValidateConfiguration()
    {
        var config = GetNoiseConfig();
        if (config == null)
        {
            EditorUtility.DisplayDialog("配置验证", "未设置NoiseConfigSO！", "确定");
            return;
        }
        
        bool hasIssues = false;
        string issues = "发现以下问题：\n\n";
        
        // 检查地图尺寸
        if (config.MapWidth <= 0 || config.MapHeight <= 0)
        {
            issues += "• 地图尺寸必须大于0\n";
            hasIssues = true;
        }
        
        // 检查振幅数组
        if (config.ElevationAmplitudes == null || config.ElevationAmplitudes.Length == 0)
        {
            issues += "• 高度噪声振幅数组为空\n";
            hasIssues = true;
        }
        
        if (config.MoistureAmplitudes == null || config.MoistureAmplitudes.Length == 0)
        {
            issues += "• 湿度噪声振幅数组为空\n";
            hasIssues = true;
        }
        
        // 检查指数值
        if (config.ElevationExponent <= 0)
        {
            issues += "• 高度指数必须大于0\n";
            hasIssues = true;
        }
        
        // 检查缩放因子
        if (config.ElevationScale <= 0 || config.MoistureScale <= 0)
        {
            issues += "• 缩放因子必须大于0\n";
            hasIssues = true;
        }
        
        // 检查岛屿混合因子
        if (config.IslandMixFactor < 0 || config.IslandMixFactor > 1)
        {
            issues += "• 岛屿混合因子必须在0-1范围内\n";
            hasIssues = true;
        }
        
        if (hasIssues)
        {
            EditorUtility.DisplayDialog("配置验证", issues, "确定");
        }
        else
        {
            EditorUtility.DisplayDialog("配置验证", "配置验证通过！所有参数都是有效的。", "确定");
        }
    }
}
