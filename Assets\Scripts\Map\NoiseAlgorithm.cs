using UnityEngine;

/// <summary>
/// 独立的噪声算法类，实现Perlin噪声生成和相关算法
/// 基于Red Blob Games的技术方案实现
/// </summary>
public static class NoiseAlgorithm
{
    // 种子偏移量，用于生成不同的噪声图层
    private const float ELEVATION_OFFSET_X = 0f;
    private const float ELEVATION_OFFSET_Y = 0f;
    private const float MOISTURE_OFFSET_X = 1000f;
    private const float MOISTURE_OFFSET_Y = 1000f;

    /// <summary>
    /// 生成高度噪声值
    /// </summary>
    /// <param name="x">世界坐标X</param>
    /// <param name="y">世界坐标Y</param>
    /// <param name="config">噪声配置</param>
    /// <returns>0-1范围的高度值</returns>
    public static float GenerateElevationNoise(float x, float y, NoiseConfigSO config)
    {
        // 设置随机种子
        Random.InitState(config.Seed);

        // 转换为标准化坐标 (-0.5 到 0.5)
        float nx = (x / config.MapWidth - 0.5f) * config.ElevationScale;
        float ny = (y / config.MapHeight - 0.5f) * config.ElevationScale;

        // 生成多层噪声
        float elevation = GenerateOctaveNoise(nx + ELEVATION_OFFSET_X, ny + ELEVATION_OFFSET_Y, config.ElevationAmplitudes);

        // 应用指数重分布来创建平坦的山谷
        elevation = Mathf.Pow(elevation, config.ElevationExponent);

        // 如果启用岛屿模式，应用边缘平滑
        if (config.EnableIslandMode)
        {
            float distance = CalculateDistance(nx, ny, config.DistanceFunctionType);
            elevation = ApplyIslandShaping(elevation, distance, config.IslandMixFactor);
        }

        return Mathf.Clamp01(elevation);
    }

    /// <summary>
    /// 生成湿度噪声值
    /// </summary>
    /// <param name="x">世界坐标X</param>
    /// <param name="y">世界坐标Y</param>
    /// <param name="config">噪声配置</param>
    /// <returns>0-1范围的湿度值</returns>
    public static float GenerateMoistureNoise(float x, float y, NoiseConfigSO config)
    {
        // 设置随机种子（使用不同的种子确保独立性）
        Random.InitState(config.Seed + 1);

        // 转换为标准化坐标
        float nx = (x / config.MapWidth - 0.5f) * config.MoistureScale;
        float ny = (y / config.MapHeight - 0.5f) * config.MoistureScale;

        // 生成多层噪声（使用偏移确保与高度噪声独立）
        float moisture = GenerateOctaveNoise(nx + MOISTURE_OFFSET_X, ny + MOISTURE_OFFSET_Y, config.MoistureAmplitudes);

        return Mathf.Clamp01(moisture);
    }

    /// <summary>
    /// 生成多层噪声（Octave Noise）
    /// </summary>
    /// <param name="nx">标准化X坐标</param>
    /// <param name="ny">标准化Y坐标</param>
    /// <param name="amplitudes">各层振幅数组</param>
    /// <returns>合成的噪声值</returns>
    private static float GenerateOctaveNoise(float nx, float ny, float[] amplitudes)
    {
        float value = 0f;
        float frequency = 1f;

        // 累加各个频率层的噪声
        for (int i = 0; i < amplitudes.Length; i++)
        {
            value += amplitudes[i] * Mathf.PerlinNoise(nx * frequency, ny * frequency);
            frequency *= 2f; // 每层频率翻倍
        }

        // 标准化到0-1范围
        float amplitudeSum = 0f;
        foreach (float amplitude in amplitudes)
        {
            amplitudeSum += amplitude;
        }

        return value / amplitudeSum;
    }

    /// <summary>
    /// 计算距离函数，用于岛屿边缘平滑
    /// </summary>
    /// <param name="nx">标准化X坐标 (-0.5 到 0.5)</param>
    /// <param name="ny">标准化Y坐标 (-0.5 到 0.5)</param>
    /// <param name="distanceFunction">距离函数类型</param>
    /// <returns>0-1范围的距离值</returns>
    private static float CalculateDistance(float nx, float ny, NoiseConfigSO.DistanceFunction distanceFunction)
    {
        // 转换到 -1 到 1 范围
        float x = nx * 2f;
        float y = ny * 2f;

        switch (distanceFunction)
        {
            case NoiseConfigSO.DistanceFunction.SquareBump:
                // 方形凸起：适合方形地图，让岛屿填满空间但不触及边界
                return 1f - (1f - x * x) * (1f - y * y);

            case NoiseConfigSO.DistanceFunction.Euclidean:
                // 欧几里得距离：适合圆形岛屿
                return Mathf.Min(1f, (x * x + y * y) / Mathf.Sqrt(2f));

            default:
                return 0f;
        }
    }

    /// <summary>
    /// 应用岛屿塑形，混合原始高度和距离函数
    /// </summary>
    /// <param name="elevation">原始高度值</param>
    /// <param name="distance">距离值</param>
    /// <param name="mixFactor">混合因子</param>
    /// <returns>塑形后的高度值</returns>
    private static float ApplyIslandShaping(float elevation, float distance, float mixFactor)
    {
        // 线性插值：在中心(distance=0)保持原始高度，在边缘(distance=1)降为0
        float targetShape = 1f - distance;
        return Mathf.Lerp(elevation, targetShape, mixFactor);
    }
}